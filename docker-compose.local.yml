# Docker Compose 配置 - 本地开发版本
# 使用绑定挂载，便于开发和调试

version: '3.8'

services:
  tmdb-helper:
    # 使用 Docker Hub 镜像
    image: ceru007/tmdb-helper:latest
    # 如果需要本地构建，注释上面一行，取消注释下面一行
    # build: .

    container_name: tmdb-helper-local

    ports:
      - "4949:4949"

    environment:
      # 管理员账户配置
      - ADMIN_USERNAME=admin
      - ADMIN_PASSWORD=change_this_password

      # JWT 密钥（生产环境必须修改）
      - JWT_SECRET=your_jwt_secret_key_here_change_in_production

      # 会话有效期（天）
      - SESSION_EXPIRY_DAYS=7

      # Next.js 环境
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1

      # Docker 优化配置
      - DOCKER_CONTAINER=true
      - NODE_OPTIONS=--max-old-space-size=1024

      # 端口和主机配置
      - PORT=4949
      - HOSTNAME=0.0.0.0

      # TMDB API 配置（可选）
      # - TMDB_API_KEY=your_tmdb_api_key_here

    volumes:
      # 绑定挂载到宿主机目录 
      - ./data:/app/data
      - ./logs:/app/logs
      
      # 可选：挂载TMDB-Import工具目录
      # - ./TMDB-Import-master:/app/TMDB-Import-master

    restart: unless-stopped

    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4949/api/auth/init"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    # 网络配置
    networks:
      - tmdb-network

networks:
  tmdb-network:
    driver: bridge

# 注意：此配置文件使用绑定挂载
# 启动前请确保以下目录存在：
# - ./data
# - ./logs
# 
# 在Linux/macOS上，可能需要设置正确的权限：
# sudo chown -R 1001:1001 ./data ./logs
#
# 使用方法：
# docker-compose -f docker-compose.local.yml up -d
